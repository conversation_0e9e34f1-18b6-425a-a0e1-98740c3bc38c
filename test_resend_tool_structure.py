#!/usr/bin/env python3
"""
Simple test to verify the resend payment link tool structure without importing the full app.
"""

import os
import re

def test_payment_agent_tools_file():
    """Test that the payment agent tools file has the resend payment link tool."""
    file_path = "bazzuka-dc-backend/app/core/ai/payment_agent_tools.py"
    
    if not os.path.exists(file_path):
        print(f"❌ ERROR: File not found: {file_path}")
        return False
    
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Check for resend_payment_link_tool definition
    if "resend_payment_link_tool = Tool(" not in content:
        print("❌ ERROR: resend_payment_link_tool not found in payment_agent_tools.py")
        return False
    
    print("✅ SUCCESS: resend_payment_link_tool found in payment_agent_tools.py")
    
    # Check that it's registered in the tool engine
    if "engine.register(resend_payment_link_tool)" not in content:
        print("❌ ERROR: resend_payment_link_tool not registered in payment agent tool engine")
        return False
    
    print("✅ SUCCESS: resend_payment_link_tool is registered in payment agent tool engine")
    
    # Check tool parameters - look for the entire tool definition
    tool_start = content.find("resend_payment_link_tool = Tool(")
    # Find the end of the tool definition by looking for the closing parenthesis and comma
    bracket_count = 0
    tool_end = tool_start
    for i, char in enumerate(content[tool_start:]):
        if char == '(':
            bracket_count += 1
        elif char == ')':
            bracket_count -= 1
            if bracket_count == 0:
                tool_end = tool_start + i + 1
                break

    tool_section = content[tool_start:tool_end]

    required_params = ["payment_id", "issue_id", "amount"]
    for param in required_params:
        if f'"{param}"' not in tool_section:
            print(f"❌ ERROR: Parameter {param} not found in resend_payment_link_tool")
            return False
    
    print("✅ SUCCESS: All required parameters found in resend_payment_link_tool")
    
    return True

def test_payments_file():
    """Test that the payments.py file has the resend_payment_link method."""
    file_path = "bazzuka-dc-backend/app/core/payments.py"
    
    if not os.path.exists(file_path):
        print(f"❌ ERROR: File not found: {file_path}")
        return False
    
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Check for resend_payment_link method definition
    if "def resend_payment_link(self, payment_id: str, issue_id: str, amount: float):" not in content:
        print("❌ ERROR: resend_payment_link method not found in PaymentsTool class")
        return False
    
    print("✅ SUCCESS: resend_payment_link method found in PaymentsTool class")
    
    # Check that it uses the existing send_payment_link function
    method_section = content[content.find("def resend_payment_link"):content.find("def ", content.find("def resend_payment_link") + 1)]
    
    if "send_payment_link(payment_id, issue_id, amount_cents)" not in method_section:
        print("❌ ERROR: resend_payment_link method doesn't call send_payment_link function")
        return False
    
    print("✅ SUCCESS: resend_payment_link method correctly calls send_payment_link function")
    
    return True

def test_voice_agent_tools_file():
    """Test that the voice agent tools file doesn't have resend payment link tool."""
    file_path = "bazzuka-dc-backend/app/core/ai/payments_tools.py"
    
    if not os.path.exists(file_path):
        print(f"❌ ERROR: File not found: {file_path}")
        return False
    
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Check that resend_payment_link_tool is NOT in voice agent tools
    if "resend_payment_link" in content:
        print("❌ ERROR: resend_payment_link found in voice agent tools (should only be in payment agent)")
        return False
    
    print("✅ SUCCESS: resend_payment_link tool correctly NOT found in voice agent tools")
    
    return True

def main():
    """Run all tests."""
    print("Testing resend payment link tool migration structure...\n")
    
    tests = [
        ("PaymentsTool method", test_payments_file),
        ("Payment agent tools", test_payment_agent_tools_file),
        ("Voice agent tools", test_voice_agent_tools_file),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"Testing: {test_name}")
        print('='*50)
        results.append(test_func())
    
    print(f"\n{'='*50}")
    print("SUMMARY")
    print('='*50)
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"✅ ALL TESTS PASSED ({passed}/{total})")
        print("\n🎉 Resend payment link tool structure is correct!")
        print("\nThe resend payment link tool has been successfully:")
        print("  1. Added as a method to the PaymentsTool class")
        print("  2. Created as a tool in payment_agent_tools.py")
        print("  3. Registered in the payment agent tool engine")
        print("  4. Confirmed NOT to be in the voice agent tools")
    else:
        print(f"❌ SOME TESTS FAILED ({passed}/{total})")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
