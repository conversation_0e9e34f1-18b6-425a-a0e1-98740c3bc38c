"""
Test the communication preferences prompt with higher temperatures 
and different models to try to trigger false positives.
"""

import json
import openai
from typing import List, Dict, Any

# Set up OpenAI client
client = openai.OpenAI(api_key="********************************************************")

# The current prompt
COMM_PREFS_PROMPT = """Analyze the conversation transcript between a debt collector and a debtor to identify any explicit requests related to communication preferences.

**CRITICAL: Only call tools for EXPLICIT, DIRECT requests. Do NOT infer, assume, or interpret implicit preferences. When in doubt, DO NOT call any tools.**
   
- If—and only if—the debtor **explicitly** asks to stop receiving messages entirely through a specific channel (e.g., call, email, text), call the `opt_out_of_communications` tool with the appropriate channel.
    - You must always respect and act on any clear opt-out request.  
    - Example phrases include: "Please stop calling me," "Don't email me anymore," or "I no longer want to receive texts."
- If the debtor **explicitly** specifies limits on when they can be contacted (e.g., only during certain hours, days of the week, or not on holidays), call the `restrict_communications` tool with the relevant restrictions.  
    - Example phrases include: "Only call me after 5pm," "Do not contact me on weekends," or "Never call me at work."
- If you are given existing restrictions, you should append new restrictions before passing them into the `restrict_communications` tool.
- If the debtor requests not to be contacted at work, assume working hours are Monday to Friday, 9 AM to 5 PM, by default unless otherwise specified.
- **Never infer or assume a preference if the debtor does not directly state it.** For example, simply reaching voicemail, not answering a call, or automated greetings **do NOT** constitute a request or preference.
- Only act if the debtor makes a clear and affirmative request related to communication channels or timing.

**Key principle: If the debtor does not use explicit opt-out language or specific restriction requests, DO NOT call any tools.**

## Output Format
There should be no output, only tool calls if necessary."""

# Tool definitions
TOOLS = [
    {
        "type": "function",
        "function": {
            "name": "opt_out_of_communications",
            "description": "Opt a defaulter out entirely of specific communication channels.",
            "parameters": {
                "type": "object",
                "properties": {
                    "defaulter_id": {"type": "string"},
                    "channels": {"type": "array", "items": {"type": "string"}}
                },
                "required": ["defaulter_id", "channels"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "restrict_communications",
            "description": "Restrict a defaulter's communication by day of the week, time of the day, and/or holidays.",
            "parameters": {
                "type": "object",
                "properties": {
                    "defaulter_id": {"type": "string"},
                    "description": {"type": "string"}
                },
                "required": ["defaulter_id", "description"]
            }
        }
    }
]

# Challenging test cases that might trigger false positives at higher temperatures
CHALLENGING_TEST_CASES = [
    {
        'name': 'Debtor says dont want to talk but continues',
        'conversation': "Collector: Hi, this is about your account.\nDebtor: I really don't want to talk about this right now.\nCollector: I understand, but we need to resolve this.\nDebtor: Whatever, how much is it?"
    },
    {
        'name': 'Debtor mentions work repeatedly',
        'conversation': "Collector: Hi, calling about your balance.\nDebtor: I'm at work. This is my workplace. I'm working right now.\nCollector: I understand you're busy.\nDebtor: This is so annoying."
    },
    {
        'name': 'Debtor says stop in stress context',
        'conversation': "Collector: Hi, about your account.\nDebtor: Stop, stop. I'm stressed about this.\nCollector: I understand this is stressful.\nDebtor: This is a nightmare."
    },
    {
        'name': 'Debtor very hostile and mentions not wanting calls',
        'conversation': "Collector: Hi, about your account.\nDebtor: I hate you people! You're ruining my life! I don't want these calls in my life!\nCollector: I understand you're upset.\nDebtor: This is harassment!"
    },
    {
        'name': 'Debtor mentions not wanting to be bothered',
        'conversation': "Collector: Hi, calling about your debt.\nDebtor: I don't want to be bothered with this stuff right now. I'm having a bad day.\nCollector: I understand, but we need to discuss this.\nDebtor: This is making my day worse."
    },
    {
        'name': 'Debtor says leave me alone but continues',
        'conversation': "Collector: Hi, this is about your outstanding balance.\nDebtor: Just leave me alone! I'm going through a lot right now.\nCollector: I understand this is difficult, but we need to work together on this.\nDebtor: I can't deal with this stress."
    }
]


def test_configuration(model: str, temperature: float) -> Dict[str, Any]:
    """Test a specific model/temperature configuration."""
    print(f"\nTesting {model} (temperature: {temperature})")
    print("-" * 50)
    
    results = []
    false_positives = 0
    
    for test_case in CHALLENGING_TEST_CASES:
        try:
            # Create the user message
            user_message = f"""Here was the most recent communication transcript between the collector and the debtor: 

- **Defaulter ID**: test_123
- **Recent Conversation**: {test_case['conversation']}"""
            
            # Make the API call
            response = client.chat.completions.create(
                model=model,
                messages=[
                    {"role": "system", "content": COMM_PREFS_PROMPT},
                    {"role": "user", "content": user_message}
                ],
                tools=TOOLS,
                temperature=temperature
            )
            
            # Check the response
            message = response.choices[0].message
            tools_called = []
            
            if message.tool_calls:
                for tool_call in message.tool_calls:
                    tools_called.append({
                        'tool': tool_call.function.name,
                        'arguments': tool_call.function.arguments
                    })
            
            passed = len(tools_called) == 0
            if not passed:
                false_positives += 1
                print(f"❌ {test_case['name']}: Tools called - {[t['tool'] for t in tools_called]}")
            else:
                print(f"✅ {test_case['name']}: No tools called")
            
            results.append({
                'name': test_case['name'],
                'conversation': test_case['conversation'],
                'model': model,
                'temperature': temperature,
                'passed': passed,
                'tools_called': tools_called,
                'response_content': message.content
            })
            
        except Exception as e:
            print(f"❌ {test_case['name']}: Error - {str(e)}")
            results.append({
                'name': test_case['name'],
                'conversation': test_case['conversation'],
                'model': model,
                'temperature': temperature,
                'passed': False,
                'tools_called': [],
                'error': str(e)
            })
            false_positives += 1
    
    success_rate = ((len(CHALLENGING_TEST_CASES) - false_positives) / len(CHALLENGING_TEST_CASES)) * 100
    print(f"Success rate: {success_rate:.1f}% ({false_positives} false positives)")
    
    return {
        'model': model,
        'temperature': temperature,
        'results': results,
        'false_positives': false_positives,
        'success_rate': success_rate
    }


def main():
    """Run tests with different configurations."""
    print("Testing challenging cases with different model configurations...")
    print("="*80)
    
    # Test configurations - focusing on higher temperatures and different models
    configs = [
        ("gpt-4.1-mini", 0.0),
        ("gpt-4.1-mini", 0.5),
        ("gpt-4.1-mini", 0.8),
        ("gpt-4.1-mini", 1.0),
        ("gpt-4.1", 0.0),
        ("gpt-4.1", 0.5),
        ("gpt-4o-mini", 0.0),
        ("gpt-4o-mini", 0.5),
    ]
    
    all_results = []
    total_false_positives = 0
    
    for model, temperature in configs:
        try:
            config_result = test_configuration(model, temperature)
            all_results.append(config_result)
            total_false_positives += config_result['false_positives']
        except Exception as e:
            print(f"❌ Failed to test {model} (temp: {temperature}): {str(e)}")
    
    # Print summary
    print("\n" + "="*80)
    print("SUMMARY")
    print("="*80)
    
    total_tests = len(configs) * len(CHALLENGING_TEST_CASES)
    total_passed = total_tests - total_false_positives
    overall_success_rate = (total_passed / total_tests) * 100 if total_tests > 0 else 0
    
    print(f"Total configurations tested: {len(all_results)}")
    print(f"Total test cases: {total_tests}")
    print(f"Total false positives: {total_false_positives}")
    print(f"Overall success rate: {overall_success_rate:.1f}%")
    
    if total_false_positives > 0:
        print(f"\nConfigurations with false positives:")
        for result in all_results:
            if result['false_positives'] > 0:
                print(f"- {result['model']} (temp: {result['temperature']}): {result['false_positives']} false positives")
    
    # Save results
    with open('challenging_test_results.json', 'w') as f:
        json.dump(all_results, f, indent=2)
    
    print(f"\nDetailed results saved to 'challenging_test_results.json'")
    
    return all_results


if __name__ == "__main__":
    results = main()
    
    # Check if we found any false positives
    total_fps = sum(r['false_positives'] for r in results)
    if total_fps > 0:
        print(f"\n🚨 Found {total_fps} false positive cases to address!")
    else:
        print(f"\n✅ No false positives found! The current prompt is robust.")
