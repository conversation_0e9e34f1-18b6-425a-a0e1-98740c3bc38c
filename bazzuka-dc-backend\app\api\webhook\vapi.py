import os

from flask import Blueprint, request, jsonify

# from app.functions import get_summary
from app.core.comm_manager import comm_manager as manager
from app.core.ai.payments_tools import make_payment_tool_engine

vapi_webhooks_bp = Blueprint("vapi_hooks", __name__)

toolsEngine = make_payment_tool_engine()


@vapi_webhooks_bp.route("/vapi", methods=["POST"])
def webhook_route():
    if request.headers.get("X-VAPI-SECRET") != os.environ.get("VAPI_SECRET_KEY"):
        return jsonify({"error": "unauthorized"}), 401

    request_data = request.get_json()
    payload = request_data.get("message")
    # print(payload)

    if payload["type"] == "tool-calls":
        response = function_call_handler(payload)
        return jsonify(response), 201
    elif payload["type"] == "assistant-request":
        print("received assistant request")
        response = assistant_request_handler(payload)
        return jsonify(response), 201
    elif payload["type"] == "end-of-call-report":
        response = end_of_call_report_handler(payload)
        return jsonify(response), 201
    else:
        # raise ValueError("Unhandled message type")
        return jsonify({"error": "unsupported webhook"}), 201


def assistant_request_handler(payload):
    caller_number = payload["call"]["customer"]["number"]
    # TODO: direction = "inbound" if payload["call"]["type"] == "inboundPhoneCall" else "outbound"
    assistant = manager.get_assistant(caller_number)
    return {"assistant": assistant}


def function_call_handler(payload):
    """
    Handle Business logic here.
    You can handle function calls here. The payload will have function name and parameters.
    You can trigger the appropriate function based on your requirements and configurations.
    You can also have a set of validators along with each function which can be used to first validate the parameters and then call the functions.
    Here Assumption is that the functions are handling the fallback cases as well. They should return the appropriate response in case of any error.
    """
    # NOTE: We're assuming only one tool call
    function_call = payload.get("toolCalls")[0]["function"]
    tool_call_id = payload.get("toolCalls")[0]["id"]

    if not function_call:
        raise ValueError("Invalid Request.")

    name = function_call.get("name")
    parameters = function_call.get("arguments")

    print("got function call")
    print(str(name))
    print(str(parameters))

    try:
        return {
            "results": [
                {
                    "toolCallId": tool_call_id,
                    "result": toolsEngine.execute(name, parameters),
                }
            ]
        }
    except Exception as e:
        print(e)
        return {}


def end_of_call_report_handler(payload):
    print("[Webhook] Full payload:", payload)  # Debug: print the full payload

    if not payload or "call" not in payload:
        return {"message": "empty payload"}

    direction = None
    if payload["call"]["type"] == "inboundPhoneCall":
        direction = "inbound"
    elif payload["call"]["type"] == "outboundPhoneCall":
        direction = "outbound"
    if not direction:
        return {"message": "invalid call type"}

    call_id = payload["call"]["id"]
    caller_number = payload["call"]["customer"]["number"]
    timestamp = payload["call"]["createdAt"]

    artifact = payload.get("artifact", {})
    recording_url = artifact.get("recordingUrl", "")
    if not recording_url:
        print("[Warning] recordingUrl missing from payload['artifact']:", artifact)
    
    # Safely extract duration with fallback
    duration = 0
    if payload.get("costs") and len(payload["costs"]) > 0:
        duration = payload["costs"][0].get("minutes", 0)
    else:
        print("[Warning] No cost information available in payload")
    
    ended_reason = payload.get("endedReason")

    # NOTE: There is also a summary feature in VAPI...might not have to use our own. And doesn't make sense to double it up if the VAPI one is good.
    try:
        transcript = payload["artifact"]["transcript"]
    except KeyError:
        transcript = "Could not retrieve transcript"

    print(f"[Webhook] Passing ended_reason to manager.process: {ended_reason}")
    manager.process(
        "call",
        {
            "call_id": call_id,
            # "defaulter_id": defaulter_id,
            "caller_number": caller_number,
            "timestamp": timestamp,
            "direction": direction,
            "text": transcript,
            "recording_url": recording_url,
            "call_duration": duration,
            "ended_reason": ended_reason,
        },
    )

    return {}
