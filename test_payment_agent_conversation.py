#!/usr/bin/env python3
"""
Test payment agent conversation analysis for resend requests
"""

# Mock conversation examples to test
test_conversations = [
    {
        "name": "Clear Resend Request",
        "conversation": "Hi, I lost my payment link. Can you resend it to me please?",
        "expected": "Should trigger resend_payment_link tool"
    },
    {
        "name": "Alternative Resend Phrase",
        "conversation": "I didn't receive the payment link in my email. Can you send it again?",
        "expected": "Should trigger resend_payment_link tool"
    },
    {
        "name": "New Payment Request",
        "conversation": "I want to set up a payment plan for $300 per month",
        "expected": "Should trigger schedule_payment tool, NOT resend"
    },
    {
        "name": "Vague Request",
        "conversation": "I need help with my payment",
        "expected": "Should NOT trigger any payment tools"
    }
]

def analyze_conversation_intent(conversation):
    """
    Analyze what the conversation should trigger based on our prompt rules
    """
    resend_triggers = [
        "resend", "send again", "send it again", "lost", "didn't receive",
        "email me the link", "payment link again"
    ]
    
    schedule_triggers = [
        "set up", "schedule", "payment plan", "want to pay", "agree to pay"
    ]
    
    conversation_lower = conversation.lower()
    
    # Check for resend triggers
    for trigger in resend_triggers:
        if trigger in conversation_lower:
            return "resend_payment_link"
    
    # Check for schedule triggers
    for trigger in schedule_triggers:
        if trigger in conversation_lower:
            return "schedule_payment"
    
    return "no_action"

def main():
    print("Testing Payment Agent Conversation Analysis\n")
    
    for i, test in enumerate(test_conversations, 1):
        print(f"Test {i}: {test['name']}")
        print(f"Conversation: \"{test['conversation']}\"")
        
        predicted_action = analyze_conversation_intent(test['conversation'])
        print(f"Predicted Action: {predicted_action}")
        print(f"Expected: {test['expected']}")
        
        # Simple validation
        if "resend" in test['expected'].lower() and predicted_action == "resend_payment_link":
            print("✅ CORRECT: Would trigger resend tool")
        elif "schedule" in test['expected'].lower() and predicted_action == "schedule_payment":
            print("✅ CORRECT: Would trigger schedule tool")
        elif "NOT trigger" in test['expected'] and predicted_action == "no_action":
            print("✅ CORRECT: Would not trigger payment tools")
        else:
            print("⚠️  REVIEW: Check if this matches expected behavior")
        
        print("-" * 50)

if __name__ == "__main__":
    main()
