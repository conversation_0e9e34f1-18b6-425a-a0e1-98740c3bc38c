# This is just a temporary hacked solution
from app.utils.supabase.queries import get_org


class LocalStorage:
    def __init__(self):
        self.storage = {}

    def get(self, key):
        return self.storage.get(key)

    def set(self, key, value):
        self.storage[key] = value


class ProtectedStorage:
    """
    Uses a password to read the data, no password to write.
    Implements Singleton to ensure a single instance is shared globally.
    """

    _instance = None

    def __new__(cls):
        if not cls._instance:
            cls._instance = super().__new__(cls)
            cls._instance._init_storage()
        return cls._instance

    def _init_storage(self):
        self.passwords = {}
        self.storage = {}

    def get(self, key, password_type=None, password_value=None):
        # Get organization settings
        org_metadata = get_org().data[0]["metadata"]
        verification_types = org_metadata.get("verification_types", [])

        # If no verification types are configured, return value without password check
        if not verification_types:
            return self.storage.get(key)

        # If password is required but not provided
        if password_type is None or password_value is None:
            return None

        # Password was provided, verify it
        password_dict = self.passwords.get(password_type)
        if password_dict and password_dict.get(key) == password_value:
            return self.storage.get(key)
        return None

    def set(self, key, value, password_type=None, password_value=None):
        self.storage[key] = value
        if password_type and password_value:
            if password_type not in self.passwords:
                self.passwords[password_type] = {}
            self.passwords[password_type][key] = password_value

    def add_password(self, key, password_type, password_value):
        if password_type not in self.passwords:
            self.passwords[password_type] = {}
        if key in self.passwords[password_type]:
            print(f"Warning: Overwriting password for {key}")
        self.passwords[password_type][key] = password_value

    def get_all_defaulter_ids(self):
        """Return all defaulter IDs that have stored data."""
        return list(self.storage.keys())

    def remove_password(self, defaulter_id, field):
        """Remove password for a specific field for a defaulter."""
        if field in self.passwords:
            if defaulter_id in self.passwords[field]:
                del self.passwords[field][defaulter_id]


# Ensure global instance
inputs_storage = ProtectedStorage()

verified_identities = LocalStorage()

email_inputs_storage = ProtectedStorage()
