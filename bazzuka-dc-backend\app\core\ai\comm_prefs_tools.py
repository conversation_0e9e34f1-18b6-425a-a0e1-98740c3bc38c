from app.ai.tools import Too<PERSON>, <PERSON>lEng<PERSON>, ToolParam, ToolParams
from app.core.payments import PaymentsTool


opt_out_of_communications_tool = Tool(
    "opt_out_of_communications",
    "Opt a defaulter out entirely of specific communication channels. This should be used when a user requests to stop receiving communications through certain channels.",
    PaymentsTool.opt_out_of_communications,
    ToolParams(
        [
            ToolParam(
                "defaulter_id",
                "string",
                "The ID of the defaulter requesting the opt-out.",
                required=True,
            ),
            ToolParam(
                "channels",
                "array",
                "List of channels to opt out of. Can include 'calls', 'emails', and/or 'texts'.",
                required=True,
                items_type="string",
            ),
        ]
    ),
)

restrict_communications_tool = Tool(
    "restrict_communications",
    "Restrict a defaulter's communication by day of the week, time of the day, and/or holidays. This should be used when a user requests to only receive communications during certain times or on certain days.",
    PaymentsTool.restrict_communication,
    ToolParams(
        [
            ToolParam(
                "defaulter_id",
                "string",
                "The ID of the defaulter to restrict communication for.",
                required=True,
            ),
            ToolParam(
                "description",
                "string",
                "A description of the updated communication restrictions.",
                required=True,
            ),
        ]
    ),
)


def make_opt_out_tool_engine():
    engine = ToolEngine(PaymentsTool)
    engine.register(opt_out_of_communications_tool)
    engine.register(restrict_communications_tool)
    return engine


opt_out_tool_engine = make_opt_out_tool_engine()
