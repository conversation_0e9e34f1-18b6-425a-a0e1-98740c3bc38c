import React from 'react'
import classNames from 'classnames'
import { useEffect, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { UserProvider, useUser } from '../../userContext'


import {
  CAvatar,
  CButton,
  CButtonGroup,
  CCard,
  CCardBody,
  CCardFooter,
  CCardHeader,
  CCol,
  CProgress,
  CRow,
  CTable,
  CTableBody,
  CTableDataCell,
  CTableHead,
  CTableHeaderCell,
  CTableRow,
} from '@coreui/react'
import CIcon from '@coreui/icons-react'
import {
  cibCcAmex,
  cibCcApplePay,
  cibCcMastercard,
  cibCcPaypal,
  cibCcStripe,
  cibCcVisa,
  cibGoogle,
  cibFacebook,
  cibLinkedin,
  cifBr,
  cifEs,
  cifFr,
  cifIn,
  cifPl,
  cifUs,
  cibTwitter,
  cilCloudDownload,
  cilPeople,
  cilUser,
  cilUserFemale,
} from '@coreui/icons'

import avatar1 from 'src/assets/images/avatars/1.jpg'
import avatar2 from 'src/assets/images/avatars/2.jpg'
import avatar3 from 'src/assets/images/avatars/3.jpg'
import avatar4 from 'src/assets/images/avatars/4.jpg'
import avatar5 from 'src/assets/images/avatars/5.jpg'
import avatar6 from 'src/assets/images/avatars/6.jpg'

import WidgetsBrand from '../widgets/WidgetsBrand'
import WidgetsDropdown from '../widgets/WidgetsDropdown'
import MainChart from './MainChart'
import Logs from '../../components/LogsTable'
import ActionItemsTable from '../../components/ActionItemsTable'
import LogsTable from '../../components/LogsTable'

const Dashboard = () => {
  const navigate = useNavigate()

  const user = sessionStorage.getItem('access_token')
  
  useEffect(() => {
    if (!user) navigate('/login')
  }, [user, navigate])

  return (
    <UserProvider>
      <WidgetsDropdown className="mb-4" />
      
      <CRow>
        <CCol xs>
          <CCard className="mb-1">
            <CCardHeader>Scheduled</CCardHeader>
            <CCardBody>

              <ActionItemsTable />
            
            </CCardBody>
          </CCard>

          <CCard className="mb-1">
            <CCardHeader>Completed</CCardHeader>
            <CCardBody>

            <LogsTable />
            
            </CCardBody>
          </CCard>

        </CCol>
      </CRow>
    </UserProvider>
  )
}

export default Dashboard
