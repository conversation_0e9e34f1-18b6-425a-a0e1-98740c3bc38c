# Payment Agent Prompt Updates

## Overview
Updated the payment agent prompt to include guidance for the new `resend_payment_link` tool.

## Changes Made

### 1. Added "RESENDING PAYMENT LINKS" Section
**Location:** After the SCHEDULING section

**New Content:**
```
# RESENDING PAYMENT LINKS
- If the debtor **explicitly requests** to have their payment link resent, call the `resend_payment_link` tool.
- **RESEND TRIGGERS: Use the resend tool when the debtor says things like:**
    - "Can you resend my payment link?"
    - "I didn't receive the payment link"
    - "Send me the payment link again"
    - "I lost the payment link"
    - "Can you email me the link again?"
    - "I need the payment link resent"
- **REQUIREMENTS for resending:**
    - You must have the payment_id (from existing payment arrangements)
    - You must have the issue_id (from the available issues)
    - You must have the payment amount
- **IMPORTANT: Only resend links for existing payment arrangements. Do NOT create new payments when the debtor asks for a resend.**
- **PARAMETER IDENTIFICATION for resend_payment_link:**
    - payment_id: Look in the "Scheduled and Pending Payment Arrangements" section for the Payment ID
    - issue_id: Look in the "Available Issues" section for the Issue ID  
    - amount: Use the amount from the existing payment arrangement (convert from cents to dollars if needed)
- **EXAMPLE: If arrangements show "Payment ID: pay_123, Amount: $300.00" and issues show "Issue ID: 456", use:**
    - payment_id: "pay_123"
    - issue_id: "456" 
    - amount: 300.00
```

### 2. Added "AVAILABLE TOOLS SUMMARY" Section
**Location:** Before ORDER OF OPERATIONS

**New Content:**
```
# AVAILABLE TOOLS SUMMARY
You have access to these tools:
1. `schedule_payment` - Schedule new payment arrangements (one-time or recurring)
2. `schedule_one_off_communication` - Schedule future follow-up communications
3. `request_cancel_payment_arrangement` - Cancel existing payment arrangements
4. `resend_payment_link` - Resend payment links for existing arrangements
```

### 3. Updated "ORDER OF OPERATIONS" Section
**Added:** 
- If the debtor requests a payment link resend, use `resend_payment_link` (do NOT create a new payment)

## Key Guidance for the AI

### When to Use `resend_payment_link`:
- Debtor explicitly asks for payment link to be resent
- There's an existing payment arrangement to resend
- Clear trigger phrases are used

### When NOT to Use `resend_payment_link`:
- Debtor wants to make a new payment (use `schedule_payment` instead)
- No existing payment arrangement exists
- Request is vague or unclear

### Parameter Requirements:
- **payment_id**: From existing payment arrangements in context
- **issue_id**: From available issues in context  
- **amount**: From existing payment arrangement (in dollars)

### Data Sources:
The AI will look at the provided context sections:
- **"Scheduled and Pending Payment Arrangements"** → payment_id and amount
- **"Available Issues"** → issue_id

## Example Scenarios

### Scenario 1: Valid Resend Request
```
Debtor: "I lost my payment link, can you send it again?"
Context shows: Payment ID: pay_123, Amount: $300.00, Issue ID: 456

AI Action: Call resend_payment_link(payment_id="pay_123", issue_id="456", amount=300.00)
```

### Scenario 2: New Payment Request (NOT a resend)
```
Debtor: "I want to set up a payment plan"
AI Action: Call schedule_payment() with new payment details
```

### Scenario 3: Invalid Resend Request
```
Debtor: "Send me a payment link"
Context shows: No existing payment arrangements

AI Action: Do nothing or ask for clarification (no existing payment to resend)
```

## Benefits

1. **Clear Trigger Recognition**: AI knows exactly when to use resend vs schedule
2. **Parameter Guidance**: Detailed instructions on where to find required parameters
3. **Error Prevention**: Explicit warnings about not creating new payments for resend requests
4. **Context Awareness**: Uses existing payment arrangement data from the conversation context
5. **Tool Clarity**: Complete list of available tools and their purposes

## Integration with Existing Prompt

The new resend functionality integrates seamlessly with existing payment agent capabilities:
- Maintains all existing scheduling logic
- Preserves cancellation workflows  
- Adds resend capability without conflicts
- Uses same context data sources (issues and arrangements)

The payment agent can now handle the complete payment lifecycle:
- **Create** new payments (`schedule_payment`)
- **Cancel** existing payments (`request_cancel_payment_arrangement`) 
- **Resend** existing payment links (`resend_payment_link`)
- **Schedule** follow-up communications (`schedule_one_off_communication`)
