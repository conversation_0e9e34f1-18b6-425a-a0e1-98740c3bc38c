#!/usr/bin/env python3
"""
Test script to verify that the resend payment link tool has been successfully added
to the payment agent and is working correctly.
"""

import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'bazzuka-dc-backend'))

def test_resend_payment_link_tool_exists():
    """Test that the resend payment link tool is properly registered in the payment agent."""
    try:
        from app.core.ai.payment_agent_tools import payment_agent_tool_engine
        
        # Check if the tool is registered
        tools = payment_agent_tool_engine.tools
        
        print("=== Payment Agent Tools ===")
        for tool_name in tools.keys():
            print(f"- {tool_name}")
        
        # Verify resend_payment_link tool exists
        assert "resend_payment_link" in tools, "resend_payment_link tool not found in payment agent tools"
        print("\n✅ SUCCESS: resend_payment_link tool is registered in payment agent")
        
        # Check tool details
        resend_tool = tools["resend_payment_link"]
        print(f"\nTool description: {resend_tool.description}")
        
        # Verify the tool has the correct parameters
        expected_params = ["payment_id", "issue_id", "amount"]
        tool_params = [param.name for param in resend_tool.params]
        
        print(f"Tool parameters: {tool_params}")
        for param in expected_params:
            assert param in tool_params, f"Missing parameter: {param}"
        
        print("✅ SUCCESS: All required parameters are present")
        
        return True
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

def test_resend_payment_link_method_exists():
    """Test that the PaymentsTool class has the resend_payment_link method."""
    try:
        from app.core.payments import PaymentsTool
        
        # Check if the method exists
        payments_tool = PaymentsTool()
        assert hasattr(payments_tool, 'resend_payment_link'), "resend_payment_link method not found in PaymentsTool"
        
        print("✅ SUCCESS: resend_payment_link method exists in PaymentsTool")
        
        # Check method signature
        import inspect
        sig = inspect.signature(payments_tool.resend_payment_link)
        params = list(sig.parameters.keys())
        
        expected_params = ["payment_id", "issue_id", "amount"]
        print(f"Method parameters: {params}")
        
        for param in expected_params:
            assert param in params, f"Missing parameter in method: {param}"
        
        print("✅ SUCCESS: Method has correct parameters")
        
        return True
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

def test_voice_agent_tools():
    """Test that the voice agent tools don't include resend_payment_link (it should only be in payment agent)."""
    try:
        from app.core.ai.payments_tools import payment_tool_engine
        
        # Check voice agent tools
        voice_tools = payment_tool_engine.tools
        
        print("\n=== Voice Agent Tools ===")
        for tool_name in voice_tools.keys():
            print(f"- {tool_name}")
        
        # Verify resend_payment_link tool is NOT in voice agent
        assert "resend_payment_link" not in voice_tools, "resend_payment_link tool should not be in voice agent tools"
        print("\n✅ SUCCESS: resend_payment_link tool is correctly NOT in voice agent tools")
        
        return True
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

def main():
    """Run all tests."""
    print("Testing resend payment link tool migration...\n")
    
    tests = [
        test_resend_payment_link_method_exists,
        test_resend_payment_link_tool_exists,
        test_voice_agent_tools,
    ]
    
    results = []
    for test in tests:
        print(f"\n{'='*50}")
        print(f"Running: {test.__name__}")
        print('='*50)
        results.append(test())
    
    print(f"\n{'='*50}")
    print("SUMMARY")
    print('='*50)
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"✅ ALL TESTS PASSED ({passed}/{total})")
        print("\n🎉 Resend payment link tool has been successfully moved to the payment agent!")
    else:
        print(f"❌ SOME TESTS FAILED ({passed}/{total})")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
