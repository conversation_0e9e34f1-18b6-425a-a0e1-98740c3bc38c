from app.ai.client import AIClient, Action, PromptGenerator
from app.utils.openai.client import openai as openai_client
from datetime import datetime
from pydantic import BaseModel, Field
import json
from app.core.ai.comm_prefs_tools import opt_out_tool_engine
from app.utils.supabase.queries import get_restrictions_by_defaulter_id


class CommunicationPreferencesPromptGenerator(PromptGenerator):
    def __init__(self):
        super().__init__()
        self.prompt = """Analyze the conversation transcript between a debt collector and a debtor to identify any explicit requests related to communication preferences.

        **CRITICAL: Only call tools for EXPLICIT, DIRECT requests. Do NOT infer, assume, or interpret implicit preferences. When in doubt, DO NOT call any tools.**

        - If—and only if—the debtor **explicitly** asks to stop receiving messages entirely through a specific channel (e.g., call, email, text), call the `opt_out_of_communications` tool with the appropriate channel.
            - You must always respect and act on any clear opt-out request.  
            - Example phrases include: "Please stop calling me," "Don’t email me anymore," or "I no longer want to receive texts."
        - If the debtor **explicitly** specifies limits on when they can be contacted (e.g., only during certain hours, days of the week, or not on holidays), call the `restrict_communications` tool with the relevant restrictions.  
            - Example phrases include: "Only call me after 5pm," "Do not contact me on weekends," or "Never call me at work."
        - If you are given existing restrictions, you should append new restrictions before passing them into the `restrict_communications` tool.
        - If the debtor requests not to be contacted at work, assume working hours are Monday to Friday, 9 AM to 5 PM, by default unless otherwise specified.
        - **Never infer or assume a preference if the debtor does not directly state it.** For example, simply reaching voicemail, not answering a call, or automated greetings **do NOT** constitute a request or preference.
        - Only act if the debtor makes a clear and affirmative request related to communication channels or timing.
        - Be precise in identifying the channel and time preferences, and ensure that you do not make assumptions beyond what is **explicitly stated** in the conversation.

        Ensure that the extracted preferences are specific, actionable, and reflect only the debtor’s **explicitly** stated request.

        ## CRITICAL: Common False Positive Scenarios - DO NOT Call Tools For:

        **DO NOT call any tools for these scenarios:**
        - Being busy or asking to call back later: "I'm busy right now" → NO TOOLS
        - Expressing frustration without opt-out: "This is annoying" → NO TOOLS
        - Scheduling follow-up calls: "Call me tomorrow at 7 PM" → NO TOOLS
        - Voicemail or no response from user → NO TOOLS
        - General complaints about timing: "I don't have time for this" → NO TOOLS
        - Being rude or short: "Whatever, just tell me what you want" → NO TOOLS
        - Saying they can't talk now: "I can't talk right now, I'm at work" → NO TOOLS
        - Asking for information: "How much do I owe?" → NO TOOLS
        - Expressing stress: "This is really stressful" → NO TOOLS
        - Wrong number scenarios → NO TOOLS
        - Normal payment discussions → NO TOOLS

        **Key principle: If the debtor does not use explicit opt-out language or specific restriction requests, DO NOT call any tools.**

        ## Example

        ### Example Input
        - **Defaulter ID**: 12345
        - **Existing Restrictions**: Do not call on holidays.
        - **Recent Conversation**: "Please stop calling me at work!"

        ### Example Tool Call Result
        - **Tool Call**: restrict_communications
        - **Tool Params**: {"defaulter_id": 12345, "description": "Do not call on holidays and do not call during working hours (Monday to Friday, 9 AM to 5 PM)."}

        ## Output Format
        There should be no output, only tool calls if necessary.
        """
        # with open("data/prompts/opt_out.md", "r", encoding="utf-8") as file:
        #     self.prompt = file.read()
        self.inputs_prologue = "Here was the most recent communication transcript between the collector and the debtor: \n"

    def make_inputs(self, args):
        assert args.get("conversation"), f"conversation is missing or empty: {args.get('conversation')}"
        assert args.get("defaulter_id"), f"defaulter_id is missing or empty: {args.get('defaulter_id')}"
        inputs = f"""
        - **Defaulter ID**: {args["defaulter_id"]}
        - **Recent Conversation**: {args["conversation"]}
        """
        return inputs

    def generate(self, args):
        messages = []
        messages.append(
            {
                "role": "system",
                "content": self.prompt,
            }
        )
        messages.append(
            {
                "role": "user",
                "content": self.inputs_prologue + self.make_inputs(args),
            }
        )
        return messages


model = "gpt-4.1-mini"


class CommunicationPreferencesAction(Action):
    def __init__(self):
        super().__init__()
        self.prompt_generator = CommunicationPreferencesPromptGenerator()
        self.tool_engine = opt_out_tool_engine
        self.structured_output_type = None


def make_comm_preferences_client():
    client = AIClient(openai_client, model=model)
    comm_preferences_action = CommunicationPreferencesAction()
    client.register("communication_preferences_analysis", comm_preferences_action)
    return client


comm_preferences_client = make_comm_preferences_client()


def analyze_communication_preferences(conversation: str, defaulter_id: str) -> dict:
    """
    Analyze the conversation for communication preferences, including opt-outs and contact time preferences.

    Args:
        conversation: The conversation text to analyze
        defaulter_id: The ID of the defaulter

    Returns:
        dict: Analysis results containing opt_out status and any contact time preferences
    """
    
    # Skip analysis if conversation is empty or None
    if not conversation or conversation.strip() == "":
        print(f"[CommPrefs] Skipping analysis for defaulter {defaulter_id} - empty conversation")
        return {"status": "skipped", "reason": "empty_conversation"}
    
    # Ensure defaulter_id is a string
    defaulter_id = str(defaulter_id)

    existing_restrictions = get_restrictions_by_defaulter_id(defaulter_id)
    if existing_restrictions.data:
        # If there are existing restrictions, we can include them in the context
        conversation += f"\n\nExisting Restrictions: {existing_restrictions.data[0].get("description")}"

        result = (
            comm_preferences_client.do("communication_preferences_analysis")
            .with_context(
                {
                    "defaulter_id": defaulter_id,
                    "existing_restrictions": existing_restrictions,
                    "conversation": conversation,
                }
            )
            .execute()
        )
    else:
        # If no existing restrictions, we can just analyze the conversation
        result = (
            comm_preferences_client.do("communication_preferences_analysis")
            .with_context(
                {
                    "defaulter_id": defaulter_id,
                    "conversation": conversation,
                }
            )
            .execute()
        )

    return result
